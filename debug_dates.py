#!/usr/bin/env python3
"""
调试日期问题的脚本
"""
from datetime import datetime, timedelta

def analyze_dates():
    """分析日期问题"""
    print("=== 日期分析 ===")
    
    # 当前日期
    today = datetime.now()
    print(f"今天是: {today.strftime('%Y-%m-%d %A')}")
    print(f"今天是周{today.weekday() + 1} (0=周一, 6=周日)")
    
    print("\n=== 最近10天分析 ===")
    for i in range(10):
        date = today - timedelta(days=i)
        weekday = date.weekday()  # 0=Monday, 6=Sunday
        is_workday = weekday < 5
        print(f"{date.strftime('%Y-%m-%d %A')}: 工作日={is_workday}")
    
    print("\n=== 最近10天的工作日 ===")
    workdays = []
    for i in range(10):
        date = today - timedelta(days=i)
        if date.weekday() < 5:  # 工作日
            workdays.append(date.strftime('%Y-%m-%d'))
    
    print(f"工作日列表: {workdays}")
    print(f"工作日数量: {len(workdays)}")
    
    print("\n=== 7月29日之后的日期 ===")
    start_date = datetime(2025, 7, 29)
    dates_after_729 = []
    
    for i in range(10):  # 检查7月29日之后的10天
        date = start_date + timedelta(days=i)
        weekday = date.weekday()
        is_workday = weekday < 5
        dates_after_729.append({
            'date': date.strftime('%Y-%m-%d'),
            'weekday': date.strftime('%A'),
            'is_workday': is_workday
        })
        print(f"{date.strftime('%Y-%m-%d %A')}: 工作日={is_workday}")
    
    workdays_after_729 = [d['date'] for d in dates_after_729 if d['is_workday']]
    print(f"\n7月29日之后的工作日: {workdays_after_729}")
    print(f"工作日数量: {len(workdays_after_729)}")

if __name__ == "__main__":
    analyze_dates()

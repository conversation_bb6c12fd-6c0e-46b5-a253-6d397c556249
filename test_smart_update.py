#!/usr/bin/env python3
"""
测试智能更新功能
"""
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.strategy.data.collector import DataCollector

def test_smart_update():
    """测试智能更新功能"""
    print("=== 测试智能更新功能 ===")
    
    try:
        # 创建数据收集器
        collector = DataCollector()
        
        print("1. 测试获取最近交易日...")
        # 直接调用MiniQMT的方法
        qmt_collector = collector._qmt_collector
        recent_days = qmt_collector._get_recent_trading_days(10)
        print(f"最近10个交易日: {recent_days}")
        
        print("\n2. 检查已存在的数据文件...")
        existing_files = qmt_collector._check_existing_trading_day_files(recent_days)
        missing_days = [day for day in recent_days if day not in existing_files]
        
        print(f"已存在的文件: {existing_files}")
        print(f"缺失的文件: {missing_days}")
        
        print("\n3. 执行智能更新...")
        result = collector.smart_update_recent_data(10)
        
        print(f"\n智能更新结果:")
        print(f"  总交易日: {result['total_trading_days']}")
        print(f"  已存在: {result['existing_days']}")
        print(f"  需更新: {result['missing_days']}")
        print(f"  更新成功: {len(result['updated_days'])}")
        print(f"  更新失败: {len(result['failed_days'])}")
        print(f"  整体成功: {result['success']}")
        
        if result['updated_days']:
            print(f"  成功更新的交易日: {result['updated_days']}")
        if result['failed_days']:
            print(f"  更新失败的交易日: {result['failed_days']}")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_missing_data():
    """分析缺失数据的原因"""
    print("\n=== 分析缺失数据的原因 ===")
    
    # 检查7月29日之后的数据
    start_date = datetime(2025, 7, 29)
    
    print("7月29日之后的交易日:")
    for i in range(10):
        date = start_date + timedelta(days=i)
        if date.weekday() < 5:  # 工作日
            date_str = date.strftime('%Y-%m-%d')
            year = date.year
            month = f"{date.month:02d}"
            day = f"{date.day:02d}"
            
            file_name = f"{year}{month}{day}.parquet"
            
            # 检查7月和8月目录
            july_path = Path(f"data/strategy/raw/{year}/07/{file_name}")
            august_path = Path(f"data/strategy/raw/{year}/08/{file_name}")
            
            july_exists = july_path.exists()
            august_exists = august_path.exists()
            
            print(f"{date_str} ({date.strftime('%A')}):")
            print(f"  7月目录: {july_path} {'✓' if july_exists else '✗'}")
            print(f"  8月目录: {august_path} {'✓' if august_exists else '✗'}")

if __name__ == "__main__":
    test_smart_update()
    analyze_missing_data()

#!/usr/bin/env python3
"""
问题分析脚本
"""
from datetime import datetime, timedelta

def analyze_update_recent_data_issue():
    """分析 update_recent_data 的问题"""
    print("=== update_recent_data(10) 问题分析 ===")
    
    # 模拟当前的实现
    days = 10
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
    
    print(f"当前实现的日期范围:")
    print(f"开始日期: {start_date}")
    print(f"结束日期: {end_date}")
    
    # 分析这个范围包含的日期
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    
    print(f"\n包含的所有日期:")
    current = start_dt
    all_dates = []
    workdays = []
    
    while current <= end_dt:
        date_str = current.strftime("%Y-%m-%d")
        is_workday = current.weekday() < 5
        all_dates.append(date_str)
        if is_workday:
            workdays.append(date_str)
        
        status = "工作日" if is_workday else "周末"
        print(f"{current.strftime('%Y-%m-%d %A')}: {status}")
        current += timedelta(days=1)
    
    print(f"\n统计:")
    print(f"总日期数: {len(all_dates)}")
    print(f"工作日数: {len(workdays)}")
    print(f"周末数: {len(all_dates) - len(workdays)}")
    
    print(f"\n工作日列表: {workdays}")
    
    # 分析为什么只有4个文件
    print(f"\n=== 为什么只有4个文件？ ===")
    print(f"从日志看，API返回了20608条记录，按日期分组后只有4个文件")
    print(f"这说明QMT API可能只返回了4个交易日的数据")
    
    # 检查最近的交易日
    print(f"\n=== 最近的交易日分析 ===")
    today = datetime.now()
    recent_workdays = []
    
    # 往前找最近的10个工作日
    i = 0
    while len(recent_workdays) < 10:
        date = today - timedelta(days=i)
        if date.weekday() < 5:  # 工作日
            recent_workdays.append(date.strftime('%Y-%m-%d'))
        i += 1
    
    print(f"最近10个工作日:")
    for i, date in enumerate(recent_workdays):
        print(f"{i+1}. {date}")
    
    # 分析7月29日之后的情况
    print(f"\n=== 7月29日之后的交易日 ===")
    july_29 = datetime(2025, 7, 29)
    trading_days_after_729 = []
    
    for i in range(15):  # 检查之后的15天
        date = july_29 + timedelta(days=i)
        if date.weekday() < 5:  # 工作日
            trading_days_after_729.append(date.strftime('%Y-%m-%d'))
            print(f"{date.strftime('%Y-%m-%d %A')}: 交易日")
    
    print(f"\n7月29日之后的交易日: {trading_days_after_729}")
    
    # 分析可能的原因
    print(f"\n=== 可能的原因分析 ===")
    print(f"1. QMT API数据延迟: 最新的交易日数据可能还没有更新")
    print(f"2. 数据源问题: QMT可能没有最新几天的数据")
    print(f"3. 节假日影响: 可能有特殊的节假日安排")
    print(f"4. API限制: QMT可能限制了返回的数据范围")

if __name__ == "__main__":
    analyze_update_recent_data_issue()

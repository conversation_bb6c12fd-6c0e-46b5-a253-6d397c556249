#!/usr/bin/env python3
"""
修复缺失数据的脚本
"""
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.strategy.data.collector import DataCollector

def fix_missing_data():
    """修复缺失的数据"""
    print("=== 修复缺失数据 ===")
    
    try:
        # 创建数据收集器
        collector = DataCollector()
        
        print("1. 分析当前数据状况...")
        
        # 检查最近15个交易日
        qmt_collector = collector._qmt_collector
        recent_days = qmt_collector._get_recent_trading_days(15)
        
        print(f"最近15个交易日: {recent_days}")
        
        # 检查已存在的文件
        existing_files = qmt_collector._check_existing_trading_day_files(recent_days)
        missing_days = [day for day in recent_days if day not in existing_files]
        
        print(f"已存在数据的交易日 ({len(existing_files)}): {existing_files}")
        print(f"缺失数据的交易日 ({len(missing_days)}): {missing_days}")
        
        if not missing_days:
            print("✅ 所有数据都已存在，无需修复")
            return
        
        print(f"\n2. 开始修复缺失的 {len(missing_days)} 个交易日数据...")
        
        success_count = 0
        failed_count = 0
        
        for i, date_str in enumerate(missing_days, 1):
            print(f"\n[{i}/{len(missing_days)}] 修复交易日 {date_str} 的数据...")
            
            try:
                # 单独获取这一天的数据
                result = collector.collect_all_stocks_data(
                    start_date=date_str,
                    end_date=date_str,
                    batch_size=500,
                    validate_data=False
                )
                
                if result and any(result.values()):
                    success_count += 1
                    print(f"  ✅ {date_str}: 修复成功")
                    
                    # 验证文件是否创建
                    dt = datetime.strptime(date_str, "%Y-%m-%d")
                    year = dt.year
                    month = f"{dt.month:02d}"
                    day = f"{dt.day:02d}"
                    
                    file_name = f"{year}{month}{day}.parquet"
                    file_path = Path(f"data/strategy/raw/{year}/{month}/{file_name}")
                    
                    if file_path.exists():
                        import pandas as pd
                        df = pd.read_parquet(file_path)
                        print(f"    文件已创建: {file_path}")
                        print(f"    数据量: {len(df)} 条记录, {df['symbol'].nunique()} 只股票")
                    else:
                        print(f"    ⚠️ 文件未创建: {file_path}")
                else:
                    failed_count += 1
                    print(f"  ❌ {date_str}: 修复失败 - 无数据返回")
                    
            except Exception as e:
                failed_count += 1
                print(f"  ❌ {date_str}: 修复失败 - {e}")
        
        print(f"\n=== 修复完成 ===")
        print(f"总计: {len(missing_days)} 个交易日")
        print(f"成功: {success_count} 个")
        print(f"失败: {failed_count} 个")
        print(f"成功率: {success_count/len(missing_days)*100:.1f}%")
        
        # 再次检查修复结果
        print(f"\n3. 验证修复结果...")
        final_existing = qmt_collector._check_existing_trading_day_files(recent_days)
        final_missing = [day for day in recent_days if day not in final_existing]
        
        print(f"修复后已存在: {len(final_existing)} 个交易日")
        print(f"修复后仍缺失: {len(final_missing)} 个交易日")
        
        if final_missing:
            print(f"仍缺失的交易日: {final_missing}")
            print("\n可能的原因:")
            print("1. QMT数据源没有这些日期的数据")
            print("2. 这些日期可能是节假日或非交易日")
            print("3. QMT API访问限制或网络问题")
        else:
            print("🎉 所有数据修复完成！")
            
    except Exception as e:
        print(f"修复过程中出错: {e}")
        import traceback
        traceback.print_exc()

def check_data_integrity():
    """检查数据完整性"""
    print("\n=== 检查数据完整性 ===")
    
    try:
        import pandas as pd
        
        # 检查最近的数据文件
        data_path = Path('data/strategy/raw/2025')
        
        for month_dir in ['07', '08']:
            month_path = data_path / month_dir
            if month_path.exists():
                files = sorted(month_path.glob('*.parquet'))
                print(f"\n{month_dir}月数据文件 ({len(files)} 个):")
                
                for file_path in files[-5:]:  # 显示最近5个文件
                    try:
                        df = pd.read_parquet(file_path)
                        print(f"  {file_path.name}: {len(df)} 条记录, {df['symbol'].nunique()} 只股票")
                    except Exception as e:
                        print(f"  {file_path.name}: 读取失败 - {e}")
            else:
                print(f"\n{month_dir}月目录不存在")
                
    except Exception as e:
        print(f"检查数据完整性时出错: {e}")

if __name__ == "__main__":
    fix_missing_data()
    check_data_integrity()

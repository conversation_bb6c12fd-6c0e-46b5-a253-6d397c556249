"""
批量离线计算指标脚本

预计算所有股票的技术指标并存储到数据库中
"""
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.services.offline_calculator import OfflineIndicatorCalculator
from app.strategy.services.data_service import data_service

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量计算股票技术指标')
    parser.add_argument('--symbols', nargs='+', help='指定股票代码列表')
    parser.add_argument('--limit', type=int, help='限制处理的股票数量')
    parser.add_argument('--workers', type=int, default=4, help='并发线程数 (默认: 4)')
    parser.add_argument('--batch-size', type=int, default=100, help='批次大小 (默认: 100)')
    parser.add_argument('--force', action='store_true', help='强制重新计算所有指标')
    
    args = parser.parse_args()
    
    print("🚀 批量离线指标计算脚本")
    print("=" * 50)
    
    try:
        # 获取股票列表
        if args.symbols:
            symbols = args.symbols
            print(f"📋 指定股票: {len(symbols)} 只")
        else:
            stock_list = data_service.get_stock_list()
            symbols = [stock['symbol'] for stock in stock_list]
            print(f"📋 获取股票列表: {len(symbols)} 只")
        
        # 限制数量
        if args.limit:
            symbols = symbols[:args.limit]
            print(f"📊 限制处理: {len(symbols)} 只")
        
        # 开始批量计算
        print(f"⚙️ 配置: 并发数={args.workers}, 批次大小={args.batch_size}")
        
        if args.force:
            print("🔄 强制重新计算模式")
        
        confirm = input(f"\n确认开始批量计算？(y/n): ").lower().strip()
        if confirm != 'y':
            print("已取消")
            return
        
        # 创建计算器实例
        calculator = OfflineIndicatorCalculator()

        # 执行批量计算
        result = calculator.batch_calculate_indicators(
            symbols=symbols,
            max_workers=args.workers,
            batch_size=args.batch_size
        )
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎉 批量计算完成!")
        print(f"📊 总计: {result['total']} 只股票")
        print(f"✅ 成功: {result['success']} 只")
        print(f"❌ 失败: {result['failed']} 只")
        print(f"⏱ 耗时: {result['elapsed_time']:.1f} 秒")
        print(f"📈 成功率: {result['success_rate']*100:.1f}%")
        
        if result['success'] > 0:
            print("\n💡 现在可以启动Web服务使用预计算的指标:")
            print("python -m uvicorn app.main:app --reload")
        
    except KeyboardInterrupt:
        print("\n⚠ 用户中断")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭数据库连接
        try:
            if 'calculator' in locals():
                calculator.close()
        except:
            pass

if __name__ == "__main__":
    main()

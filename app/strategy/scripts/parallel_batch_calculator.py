"""
高性能并行批量指标计算脚本

支持多进程并行计算和增量更新
"""
import sys
import gc
import psutil
import argparse
import multiprocessing as mp
from pathlib import Path
import time
import logging
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor, as_completed
import duckdb

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.services.data_service import data_service
from app.strategy.indicators.engine import IndicatorEngine

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ParallelBatchCalculator:
    """高性能并行批量计算器"""
    
    def __init__(self):
        self.db_path = Path("data/strategy/processed/indicators.duckdb")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.processed_count = 0
        self.failed_count = 0
        self.start_time = time.time()
    
    def get_existing_symbols(self):
        """获取已存在指标数据的股票列表"""
        try:
            # 使用只读连接避免锁冲突
            with duckdb.connect(str(self.db_path), read_only=True) as conn:
                result = conn.execute("SELECT DISTINCT symbol FROM stock_indicators").fetchall()
                return [row[0] for row in result]
        except Exception as e:
            logger.warning(f"获取已存在股票列表失败: {e}")
            return []
    
    def get_latest_date_for_symbol(self, symbol: str):
        """获取股票的最新数据日期"""
        try:
            # 使用只读连接避免锁冲突
            with duckdb.connect(str(self.db_path), read_only=True) as conn:
                result = conn.execute(
                    "SELECT MAX(date) FROM stock_indicators WHERE symbol = ?",
                    [symbol]
                ).fetchone()
                return result[0] if result and result[0] else None
        except Exception as e:
            logger.warning(f"获取股票 {symbol} 最新日期失败: {e}")
            return None
    
    def should_update_symbol(self, symbol: str, force_update: bool = False):
        """判断是否需要更新股票数据"""
        if force_update:
            return True
        
        latest_date = self.get_latest_date_for_symbol(symbol)
        if not latest_date:
            return True
        
        # 如果最新数据超过1天，则需要更新
        today = datetime.now().date()
        if isinstance(latest_date, str):
            latest_date = datetime.strptime(latest_date, '%Y-%m-%d').date()
        
        return (today - latest_date).days > 0
    
    def calculate_single_stock_parallel(self, args):
        """并行计算单只股票指标（独立进程函数）"""
        symbol, force_update, start_date = args

        try:
            # 在子进程中重新初始化服务，使用独立的数据库连接
            from app.strategy.services.offline_calculator import OfflineIndicatorCalculator

            calculator = OfflineIndicatorCalculator()

            # 使用进程安全的方式检查是否需要更新
            if not force_update:
                try:
                    # 使用只读连接检查更新状态
                    should_update = calculator._should_update_symbol_safe(symbol)
                    if not should_update:
                        calculator.close()
                        return {'symbol': symbol, 'status': 'skipped', 'message': '数据已是最新'}
                except Exception as e:
                    # 如果检查失败，继续计算
                    pass

            # 计算指标，使用进程安全的方式
            success = calculator.calculate_single_stock_indicators_safe(symbol, start_date)

            calculator.close()

            if success:
                return {'symbol': symbol, 'status': 'success', 'message': '计算成功'}
            else:
                return {'symbol': symbol, 'status': 'failed', 'message': '计算失败'}

        except Exception as e:
            return {'symbol': symbol, 'status': 'error', 'message': str(e)}
    
    def batch_calculate_parallel(self, symbols: list, 
                                max_workers: int = None, 
                                batch_size: int = 100,
                                force_update: bool = False,
                                incremental: bool = True):
        """高性能并行批量计算"""
        
        if max_workers is None:
            max_workers = min(mp.cpu_count(), 8)  # 最多8个进程
        
        print(f"🚀 开始高性能并行计算...")
        print(f"📊 总股票数: {len(symbols)}")
        print(f"⚙️ 并行进程数: {max_workers}")
        print(f"📦 批次大小: {batch_size}")
        print(f"🔄 强制更新: {force_update}")
        print(f"📈 增量模式: {incremental}")
        
        # 过滤需要更新的股票
        if incremental and not force_update:
            print("🔍 检查需要更新的股票...")
            symbols_to_update = []
            for symbol in symbols:
                if self.should_update_symbol(symbol, force_update):
                    symbols_to_update.append(symbol)
            
            print(f"📊 需要更新: {len(symbols_to_update)}/{len(symbols)} 只股票")
            symbols = symbols_to_update
        
        if not symbols:
            print("✅ 所有股票数据都是最新的")
            return {
                'total': 0,
                'success': 0,
                'failed': 0,
                'skipped': 0,
                'elapsed_time': 0,
                'success_rate': 1.0
            }
        
        # 准备参数
        start_date = None
        if incremental:
            # 增量模式：只计算最近30天的数据
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        args_list = [(symbol, force_update, start_date) for symbol in symbols]
        
        # 分批并行处理
        total_batches = (len(args_list) + batch_size - 1) // batch_size
        success_count = 0
        failed_count = 0
        skipped_count = 0
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(args_list))
            batch_args = args_list[start_idx:end_idx]
            
            print(f"\n📦 处理批次 {batch_idx + 1}/{total_batches} ({len(batch_args)} 只股票)")
            
            # 并行处理当前批次
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                futures = {
                    executor.submit(self.calculate_single_stock_parallel, args): args[0]
                    for args in batch_args
                }
                
                for future in as_completed(futures):
                    symbol = futures[future]
                    try:
                        result = future.result()
                        
                        if result['status'] == 'success':
                            success_count += 1
                            print(f"  ✓ {symbol}: {result['message']}")
                        elif result['status'] == 'skipped':
                            skipped_count += 1
                            print(f"  ⏭ {symbol}: {result['message']}")
                        else:
                            failed_count += 1
                            print(f"  ❌ {symbol}: {result['message']}")
                            
                    except Exception as e:
                        failed_count += 1
                        print(f"  ❌ {symbol}: 进程异常 - {e}")
            
            # 批次间休息
            if batch_idx < total_batches - 1:
                print(f"  ⏸ 批次完成，休息3秒...")
                time.sleep(3)
                
                # 显示进度
                total_processed = success_count + failed_count + skipped_count
                progress = (batch_idx + 1) / total_batches * 100
                print(f"  📊 进度: {progress:.1f}% ({total_processed}/{len(symbols)})")
        
        elapsed_time = time.time() - self.start_time
        total_processed = success_count + failed_count + skipped_count
        
        return {
            'total': len(symbols),
            'success': success_count,
            'failed': failed_count,
            'skipped': skipped_count,
            'elapsed_time': elapsed_time,
            'success_rate': success_count / len(symbols) if symbols else 1.0
        }

def calculate_single_stock_worker(args):
    """工作进程函数（必须在模块级别定义）"""
    symbol, force_update, start_date = args

    try:
        # 在子进程中重新初始化
        import sys
        from pathlib import Path

        project_root = Path(__file__).parent.parent.parent.parent
        sys.path.insert(0, str(project_root))

        from app.strategy.services.offline_calculator import OfflineIndicatorCalculator

        calculator = OfflineIndicatorCalculator()

        # 使用进程安全的方式检查是否需要更新
        if not force_update:
            try:
                should_update = calculator._should_update_symbol_safe(symbol)
                if not should_update:
                    calculator.close()
                    return {'symbol': symbol, 'status': 'skipped', 'message': '数据已是最新'}
            except Exception:
                # 如果检查失败，继续计算
                pass

        # 使用进程安全的计算方法
        success = calculator.calculate_single_stock_indicators_safe(symbol, start_date)

        calculator.close()

        if success:
            return {'symbol': symbol, 'status': 'success', 'message': '计算成功'}
        else:
            return {'symbol': symbol, 'status': 'failed', 'message': '计算失败'}

    except Exception as e:
        return {'symbol': symbol, 'status': 'error', 'message': str(e)}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='高性能并行批量计算股票技术指标')
    parser.add_argument('--symbols', nargs='+', help='指定股票代码列表')
    parser.add_argument('--limit', type=int, help='限制处理的股票数量')
    parser.add_argument('--workers', type=int, help='并行进程数 (默认: CPU核心数)')
    parser.add_argument('--batch-size', type=int, default=50, help='批次大小 (默认: 50)')
    parser.add_argument('--start-from', type=int, default=0, help='从第N只股票开始')
    parser.add_argument('--force', action='store_true', help='强制重新计算所有指标')
    parser.add_argument('--full', action='store_true', help='全量模式（非增量）')
    
    args = parser.parse_args()
    
    print("⚡ 高性能并行指标计算脚本")
    print("=" * 50)
    
    try:
        # 获取股票列表
        if args.symbols:
            symbols = args.symbols
            print(f"📋 指定股票: {len(symbols)} 只")
        else:
            stock_list = data_service.get_stock_list()
            symbols = [stock['symbol'] for stock in stock_list]
            print(f"📋 获取股票列表: {len(symbols)} 只")
        
        # 应用限制和起始位置
        if args.start_from > 0:
            symbols = symbols[args.start_from:]
            print(f"📍 从第 {args.start_from + 1} 只股票开始")
        
        if args.limit:
            symbols = symbols[:args.limit]
            print(f"📊 限制处理: {len(symbols)} 只")
        
        # 设置并行参数
        max_workers = args.workers or min(mp.cpu_count(), 8)
        print(f"⚙️ 并行进程数: {max_workers}")
        print(f"📦 批次大小: {args.batch_size}")
        
        # 显示系统信息
        memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
        print(f"💾 当前内存使用: {memory_mb:.1f}MB")
        print(f"🖥️ CPU核心数: {mp.cpu_count()}")
        
        confirm = input(f"\n确认开始高性能并行计算？(y/n): ").lower().strip()
        if confirm != 'y':
            print("已取消")
            return
        
        # 执行并行计算
        calculator = ParallelBatchCalculator()
        result = calculator.batch_calculate_parallel(
            symbols=symbols,
            max_workers=max_workers,
            batch_size=args.batch_size,
            force_update=args.force,
            incremental=not args.full
        )
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎉 高性能并行计算完成!")
        print(f"📊 总计: {result['total']} 只股票")
        print(f"✅ 成功: {result['success']} 只")
        print(f"❌ 失败: {result['failed']} 只")
        print(f"⏭ 跳过: {result['skipped']} 只")
        print(f"⏱ 耗时: {result['elapsed_time']:.1f} 秒")
        print(f"📈 成功率: {result['success_rate']*100:.1f}%")
        
        if result['total'] > 0:
            speed = result['total'] / result['elapsed_time']
            print(f"🚀 处理速度: {speed:.1f} 只/秒")
        
        if result['success'] > 0:
            print("\n💡 现在可以启动Web服务使用预计算的指标:")
            print("python -m uvicorn app.main:app --reload")
        
    except KeyboardInterrupt:
        print("\n⚠ 用户中断")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logger.error(f"执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

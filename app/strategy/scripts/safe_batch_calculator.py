"""
安全的批量指标计算脚本

修复内存泄漏和数据异常问题
"""
import sys
import gc
import psutil
import argparse
from pathlib import Path
import time
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.services.offline_calculator import OfflineIndicatorCalculator
from app.strategy.services.data_service import data_service

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SafeBatchCalculator:
    """安全的批量计算器"""
    
    def __init__(self):
        self.max_memory_mb = 2048  # 最大内存使用2GB
        self.processed_count = 0
        self.failed_count = 0
        self.start_time = time.time()
        self.calculator = OfflineIndicatorCalculator()
    
    def check_memory_usage(self):
        """检查内存使用情况"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        if memory_mb > self.max_memory_mb:
            logger.warning(f"内存使用过高: {memory_mb:.1f}MB, 执行垃圾回收")
            gc.collect()
            
            # 再次检查
            memory_mb = process.memory_info().rss / 1024 / 1024
            if memory_mb > self.max_memory_mb:
                logger.error(f"内存使用仍然过高: {memory_mb:.1f}MB")
                return False
        
        return True
    
    def safe_calculate_single_stock(self, symbol: str) -> bool:
        """安全地计算单只股票指标"""
        try:
            print(f"  处理股票: {symbol}")
            
            # 检查内存
            if not self.check_memory_usage():
                logger.error(f"内存不足，跳过股票 {symbol}")
                return False
            
            # 加载数据前先验证
            stock_data = data_service.load_stock_history(symbol, limit=300)
            
            if stock_data.empty:
                print(f"    ⚠ 股票 {symbol} 无数据")
                return False
            
            # 数据质量检查
            if len(stock_data) < 20:
                print(f"    ⚠ 股票 {symbol} 数据不足: {len(stock_data)} 条")
                return False
            
            # 检查关键字段
            required_fields = ['open', 'high', 'low', 'close', 'volume']
            for field in required_fields:
                if field not in stock_data.columns:
                    print(f"    ⚠ 股票 {symbol} 缺少字段: {field}")
                    return False
                
                # 检查数据类型
                if not stock_data[field].dtype.kind in 'biufc':  # 数值类型
                    print(f"    ⚠ 股票 {symbol} 字段 {field} 类型异常: {stock_data[field].dtype}")
                    return False
                
                # 检查空值比例
                null_ratio = stock_data[field].isnull().sum() / len(stock_data)
                if null_ratio > 0.5:
                    print(f"    ⚠ 股票 {symbol} 字段 {field} 空值过多: {null_ratio:.1%}")
                    return False
            
            # 检查价格逻辑
            invalid_prices = (
                (stock_data['high'] < stock_data['low']) |
                (stock_data['close'] <= 0) |
                (stock_data['high'] <= 0) |
                (stock_data['low'] <= 0)
            ).sum()
            
            if invalid_prices > len(stock_data) * 0.1:
                print(f"    ⚠ 股票 {symbol} 价格数据异常: {invalid_prices}/{len(stock_data)}")
                return False
            
            # 计算指标
            success = self.calculator.calculate_single_stock_indicators(symbol)
            
            if success:
                print(f"    ✓ 股票 {symbol} 计算成功")
                self.processed_count += 1
            else:
                print(f"    ❌ 股票 {symbol} 计算失败")
                self.failed_count += 1
            
            # 强制垃圾回收
            del stock_data
            gc.collect()
            
            return success
            
        except Exception as e:
            print(f"    ❌ 股票 {symbol} 异常: {e}")
            logger.error(f"计算股票 {symbol} 异常: {e}")
            self.failed_count += 1
            
            # 清理内存
            gc.collect()
            return False
    
    def batch_calculate_safe(self, symbols: list, batch_size: int = 50):
        """安全的批量计算"""
        print(f"🚀 开始安全批量计算...")
        print(f"📊 总股票数: {len(symbols)}")
        print(f"📦 批次大小: {batch_size}")
        
        total_batches = (len(symbols) + batch_size - 1) // batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(symbols))
            batch_symbols = symbols[start_idx:end_idx]
            
            print(f"\n📦 处理批次 {batch_idx + 1}/{total_batches} ({len(batch_symbols)} 只股票)")
            
            # 逐个处理（避免并发导致的内存问题）
            for symbol in batch_symbols:
                try:
                    self.safe_calculate_single_stock(symbol)
                    
                    # 每10只股票休息一下
                    if (self.processed_count + self.failed_count) % 10 == 0:
                        time.sleep(0.5)
                        
                    # 每50只股票强制垃圾回收
                    if (self.processed_count + self.failed_count) % 50 == 0:
                        gc.collect()
                        memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
                        print(f"    💾 内存使用: {memory_mb:.1f}MB")
                        
                except KeyboardInterrupt:
                    print(f"\n⚠ 用户中断")
                    return self.get_stats()
                except Exception as e:
                    print(f"    ❌ 批次处理异常: {e}")
                    logger.error(f"批次处理异常: {e}")
                    continue
            
            # 批次间休息
            if batch_idx < total_batches - 1:
                print(f"  ⏸ 批次完成，休息5秒...")
                time.sleep(5)
                gc.collect()  # 批次间强制垃圾回收
        
        return self.get_stats()
    
    def get_stats(self):
        """获取统计信息"""
        elapsed_time = time.time() - self.start_time
        total_processed = self.processed_count + self.failed_count
        
        return {
            'total_processed': total_processed,
            'success_count': self.processed_count,
            'failed_count': self.failed_count,
            'success_rate': self.processed_count / total_processed if total_processed > 0 else 0,
            'elapsed_time': elapsed_time
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='安全批量计算股票技术指标')
    parser.add_argument('--symbols', nargs='+', help='指定股票代码列表')
    parser.add_argument('--limit', type=int, help='限制处理的股票数量')
    parser.add_argument('--batch-size', type=int, default=50, help='批次大小 (默认: 50)')
    parser.add_argument('--start-from', type=int, default=0, help='从第N只股票开始')
    
    args = parser.parse_args()
    
    print("🛡️ 安全批量指标计算脚本")
    print("=" * 50)
    
    try:
        # 获取股票列表
        if args.symbols:
            symbols = args.symbols
            print(f"📋 指定股票: {len(symbols)} 只")
        else:
            stock_list = data_service.get_stock_list()
            symbols = [stock['symbol'] for stock in stock_list]
            print(f"📋 获取股票列表: {len(symbols)} 只")
        
        # 应用限制和起始位置
        if args.start_from > 0:
            symbols = symbols[args.start_from:]
            print(f"📍 从第 {args.start_from + 1} 只股票开始")
        
        if args.limit:
            symbols = symbols[:args.limit]
            print(f"📊 限制处理: {len(symbols)} 只")
        
        print(f"📦 批次大小: {args.batch_size}")
        
        # 显示内存信息
        memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
        print(f"💾 当前内存使用: {memory_mb:.1f}MB")
        
        confirm = input(f"\n确认开始安全批量计算？(y/n): ").lower().strip()
        if confirm != 'y':
            print("已取消")
            return
        
        # 执行安全批量计算
        calculator = SafeBatchCalculator()
        result = calculator.batch_calculate_safe(symbols, args.batch_size)
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎉 安全批量计算完成!")
        print(f"📊 总处理: {result['total_processed']} 只股票")
        print(f"✅ 成功: {result['success_count']} 只")
        print(f"❌ 失败: {result['failed_count']} 只")
        print(f"⏱ 耗时: {result['elapsed_time']:.1f} 秒")
        print(f"📈 成功率: {result['success_rate']*100:.1f}%")
        
        if result['success_count'] > 0:
            print("\n💡 现在可以启动Web服务使用预计算的指标:")
            print("python -m uvicorn app.main:app --reload")
        
    except KeyboardInterrupt:
        print("\n⚠ 用户中断")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logger.error(f"执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭数据库连接
        try:
            if 'calculator' in locals():
                calculator.close()
        except:
            pass

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
调试增量更新问题
"""
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_date_calculation():
    """调试日期计算"""
    print("=== 调试增量更新日期计算 ===")
    
    # 模拟 update_recent_data(10) 的日期计算
    days = 10
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
    
    print(f"update_recent_data({days}) 的日期范围:")
    print(f"开始日期: {start_date}")
    print(f"结束日期: {end_date}")
    
    # 计算这个范围内的所有日期
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    
    print(f"\n这个范围内的所有日期:")
    current = start_dt
    all_dates = []
    workdays = []
    
    while current <= end_dt:
        date_str = current.strftime("%Y-%m-%d")
        is_workday = current.weekday() < 5
        all_dates.append(date_str)
        if is_workday:
            workdays.append(date_str)
        
        print(f"{current.strftime('%Y-%m-%d %A')}: 工作日={is_workday}")
        current += timedelta(days=1)
    
    print(f"\n总日期数: {len(all_dates)}")
    print(f"工作日数: {len(workdays)}")
    print(f"工作日列表: {workdays}")
    
    # 检查现有文件
    print(f"\n=== 检查现有数据文件 ===")
    
    for date_str in workdays:
        dt = datetime.strptime(date_str, "%Y-%m-%d")
        year = dt.year
        month = f"{dt.month:02d}"
        day = f"{dt.day:02d}"
        
        file_name = f"{year}{month}{day}.parquet"
        file_path = Path(f"data/strategy/raw/{year}/{month}/{file_name}")
        
        exists = file_path.exists()
        print(f"{date_str} -> {file_path}: {'✓' if exists else '✗'}")
    
    # 特别检查7月29日之后的情况
    print(f"\n=== 特别检查7月29日之后 ===")
    july_29 = datetime(2025, 7, 29)
    
    for i in range(10):
        date = july_29 + timedelta(days=i)
        if date.weekday() < 5:  # 工作日
            date_str = date.strftime("%Y-%m-%d")
            year = date.year
            month = f"{date.month:02d}"
            day = f"{date.day:02d}"
            
            file_name = f"{year}{month}{day}.parquet"
            file_path = Path(f"data/strategy/raw/{year}/{month}/{file_name}")
            
            exists = file_path.exists()
            print(f"{date_str} ({date.strftime('%A')}): {file_path} {'✓' if exists else '✗'}")

def check_qmt_date_format():
    """检查QMT API的日期格式要求"""
    print(f"\n=== QMT API日期格式检查 ===")
    
    # 检查代码中使用的日期格式
    days = 10
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    print(f"Python datetime格式:")
    print(f"  开始: {start_date}")
    print(f"  结束: {end_date}")
    
    print(f"\nQMT API格式 (YYYY-MM-DD):")
    print(f"  开始: {start_date.strftime('%Y-%m-%d')}")
    print(f"  结束: {end_date.strftime('%Y-%m-%d')}")
    
    print(f"\nQMT API格式 (YYYYMMDD):")
    print(f"  开始: {start_date.strftime('%Y%m%d')}")
    print(f"  结束: {end_date.strftime('%Y%m%d')}")

if __name__ == "__main__":
    debug_date_calculation()
    check_qmt_date_format()
